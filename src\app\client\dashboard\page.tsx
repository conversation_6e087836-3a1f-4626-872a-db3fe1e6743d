"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { useDashboardStats, useProjects, useProposals, useProjectBriefs, useConnectedDesigners, useAcceptProposal, useRejectProposal } from "@/hooks/useDashboardData";
import { useResponsiveNavigation } from "@/hooks/useMobileNavigation";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";

import QualityIntegration from "@/components/integration/QualityIntegration";
import ManagerIntegration from "@/components/integration/ManagerIntegration";
import UnifiedCommunication from "@/components/integration/UnifiedCommunication";
import UnifiedFileManager from "@/components/files/UnifiedFileManager";
import { OptimizedAppIntegrationTest } from "@/components/test/OptimizedAppIntegrationTest";
import {
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  Pencil,
  ArrowRight,
  MessageSquare,
  Image,
  User,
  Users,
  Briefcase,
  Send,
  Eye,
  Star,
  Calendar,
  DollarSign,
  Target,
  Bell,
  Filter,
  Search,
  XCircle,
} from "lucide-react";

type ProjectSummary = {
  id: string;
  title: string;
  status: string;
  updated_at: string;
};

type SubmissionSummary = {
  id: string;
  title: string;
  project_id: string;
  project_title: string;
  status: string;
  created_at: string;
};

type ProjectBrief = {
  id: string;
  title: string;
  description: string;
  budget_range: string;
  timeline_preference: string;
  urgency: "low" | "medium" | "high" | "urgent";
  status:
    | "pending"
    | "assigned"
    | "proposal_received"
    | "accepted"
    | "rejected";
  assigned_designer_id: string | null;
  assigned_designer_name: string | null;
  created_at: string;
  proposal_count: number;
};

type Proposal = {
  id: string;
  brief_id: string;
  brief_title: string;
  designer_id: string;
  designer_name: string;
  designer_avatar: string | null;
  title: string;
  description: string;
  total_budget: number;
  timeline_weeks: number;
  status: "draft" | "submitted" | "under_review" | "accepted" | "rejected";
  submitted_at: string;
  expires_at: string | null;
};

type ConnectedDesigner = {
  id: string;
  full_name: string;
  avatar_url: string | null;
  specialization: string | null;
  availability_status: "available" | "busy" | "offline";
  rating: number;
  completed_projects: number;
};

export default function ClientDashboard() {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const [error, setError] = useState<string | null>(null);
  const { isMobile, isTablet } = useResponsiveNavigation();



  // Use optimized data hooks - only when user is available
  const { data: stats, isLoading: statsLoading, error: statsError } = useDashboardStats(user?.id || '', 'client');
  const { data: projects, isLoading: projectsLoading, error: projectsError } = useProjects(user?.id || '', 'client');
  const { data: proposals, isLoading: proposalsLoading, error: proposalsError } = useProposals(user?.id || '', 'client');
  const { data: projectBriefs, isLoading: briefsLoading, error: briefsError } = useProjectBriefs(user?.id || '', 'client');
  const { data: connectedDesigners, isLoading: designersLoading, error: designersError } = useConnectedDesigners(user?.id || '', 'client');

  // Proposal action hooks with correct parameters
  const acceptProposal = useAcceptProposal(user?.id || '');
  const rejectProposal = useRejectProposal();

  // Enhanced status display for client perspective
  const getClientStatusDisplay = (status: string, expiresAt: string | null = null) => {
    const isExpired = expiresAt && new Date(expiresAt) < new Date();

    if (isExpired && (status === 'submitted' || status === 'under_review')) {
      return { text: "EXPIRED", color: "text-red-600 bg-red-50 border-red-200", icon: "expired" };
    }

    switch (status) {
      case "submitted": return { text: "NEW", color: "text-blue-600 bg-blue-50 border-blue-200", icon: "new" };
      case "under_review": return { text: "REVIEWING", color: "text-yellow-600 bg-yellow-50 border-yellow-200", icon: "review" };
      case "accepted": return { text: "ACCEPTED", color: "text-green-600 bg-green-50 border-green-200", icon: "accepted" };
      case "rejected": return { text: "REJECTED", color: "text-red-600 bg-red-50 border-red-200", icon: "rejected" };
      case "withdrawn": return { text: "WITHDRAWN", color: "text-gray-600 bg-gray-50 border-gray-200", icon: "withdrawn" };
      default: return { text: status.replace("_", " ").toUpperCase(), color: "text-gray-600 bg-gray-50 border-gray-200", icon: "default" };
    }
  };

  const getProposalStatusIcon = (iconType: string) => {
    switch (iconType) {
      case "accepted": return <CheckCircle className="h-3 w-3" />;
      case "new": return <FileText className="h-3 w-3" />;
      case "review": return <Clock className="h-3 w-3" />;
      case "rejected": return <XCircle className="h-3 w-3" />;
      case "withdrawn": return <AlertCircle className="h-3 w-3" />;
      case "expired": return <AlertCircle className="h-3 w-3" />;
      default: return <Target className="h-3 w-3" />;
    }
  };

  // Derived data with enhanced filtering
  const recentProjects = projects?.slice(0, 3) || [];
  const pendingProposals = proposals?.filter(p => {
    const isExpired = p.expires_at && new Date(p.expires_at) < new Date();
    return ['submitted', 'under_review'].includes(p.status) && !isExpired;
  }) || [];
  const recentProposals = proposals?.slice(0, 5) || [];

  // Only show loading if auth is loading OR if user exists and critical data is loading
  const loading = authLoading || (user && (statsLoading || projectsLoading));

  // Show error if any critical hooks have errors
  const hasErrors = statsError || projectsError || proposalsError || briefsError || designersError;



  const handleProposalAction = async (
    proposalId: string,
    action: "accept" | "reject",
    briefId?: string
  ) => {
    if (!confirm(`Are you sure you want to ${action} this proposal?`)) {
      return;
    }

    try {
      if (action === 'accept') {
        await acceptProposal.mutateAsync({ proposalId, briefId });
      } else {
        await rejectProposal.mutateAsync({ proposalId, userId: user?.id || '' });
      }
    } catch (error) {
      console.error(`Error ${action}ing proposal:`, error);
    }
  };
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
      case "approved":
        return "bg-green-100 text-green-800";
      case "in_progress":
      case "submitted":
        return "bg-brown-100 text-brown-800";
      case "review":
      case "needs_revision":
        return "bg-yellow-100 text-yellow-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      case "cancelled":
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getProjectStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
      case "approved":
        return <CheckCircle className="h-4 w-4 mr-1" />;
      case "in_progress":
        return <Pencil className="h-4 w-4 mr-1" />;
      case "review":
      case "submitted":
        return <FileText className="h-4 w-4 mr-1" />;
      case "draft":
      case "needs_revision":
        return <Clock className="h-4 w-4 mr-1" />;
      case "cancelled":
      case "rejected":
        return <AlertCircle className="h-4 w-4 mr-1" />;
      default:
        return <Clock className="h-4 w-4 mr-1" />;
    }
  };

  // Show loading only for initial auth or critical data
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: "linear" }}
        >
          <div className="h-12 w-12 border-t-2 border-b-2 border-brown-600"></div>
        </motion.div>
        <span className="ml-3 text-gray-600">Loading your dashboard...</span>
      </div>
    );
  }

  // Show error state if there are critical errors
  if (hasErrors) {
    console.error('Dashboard errors:', { statsError, projectsError, proposalsError, briefsError, designersError });
  }

  function getUrgencyColor(urgency: string) {
    switch (urgency) {
      case "urgent":
        return "bg-red-100 text-red-800 border-red-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  }

  function getAvailabilityColor(availability_status: string) {
    switch (availability_status) {
      case "available":
        return "bg-green-100 text-green-800 border-green-200";
      case "busy":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "offline":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  }

  function renderStars(rating: number): React.ReactNode {
    const fullStars = Math.floor(rating);
    const halfStar = rating - fullStars >= 0.5;
    const totalStars = 5;
    const stars = [];

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star
          key={i}
          className="h-4 w-4 text-yellow-400 inline"
          fill="currentColor"
        />
      );
    }
    if (halfStar) {
      stars.push(
        <Star
          key="half"
          className="h-4 w-4 text-yellow-400 inline"
          fill="currentColor"
          style={{ clipPath: "inset(0 50% 0 0)" }}
        />
      );
    }
    for (let i = fullStars + (halfStar ? 1 : 0); i < totalStars; i++) {
      stars.push(<Star key={i} className="h-4 w-4 text-gray-300 inline" />);
    }
    return <span>{stars}</span>;
  }

  return (
    <div className="space-y-4 lg:space-y-6 max-w-full overflow-hidden">
      {/* Header with Primary Action - Mobile Responsive */}
      <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'justify-between items-center'}`}>
        <div>
          <h1 className={`font-bold text-gray-900 ${isMobile ? 'text-xl' : 'text-2xl'}`}>
            Client Dashboard
          </h1>
          <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}>
            Manage your project briefs and collaborate with designers
          </p>
        </div>

        {/* Actions */}
        <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'items-center space-x-4'}`}>
          <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'items-center space-x-3'}`}>
          <Link href="/client/briefs/new">
            <Button
              className={`flex items-center bg-brown-600 hover:bg-brown-700 text-white ${
                isMobile ? 'w-full justify-center' : ''
              }`}
              size={isMobile ? "sm" : "default"}
            >
              <Briefcase className="mr-2 h-4 w-4" />
              {isMobile ? 'New Brief' : 'Submit New Brief'}
            </Button>
          </Link>
          <Link href="/client/projects/new">
            <Button
              variant="outline"
              className={`flex items-center border-brown-600 text-brown-600 hover:bg-brown-50 ${
                isMobile ? 'w-full justify-center' : ''
              }`}
              size={isMobile ? "sm" : "default"}
            >
              <Plus className="mr-2 h-4 w-4" />
              {isMobile ? 'Direct Project' : 'Direct Project'}
            </Button>
          </Link>
          </div>
        </div>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-red-50 border border-red-200 p-4 mb-6 flex items-start"
        >
          <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
          <p className="text-red-700">{error}</p>
        </motion.div>
      )}

      {/* Enhanced Stats Cards - Optimized Layout */}
      <div className={`grid gap-4 max-w-full overflow-hidden ${
        isMobile
          ? 'grid-cols-2'
          : isTablet
            ? 'grid-cols-3'
            : 'grid-cols-5'
      }`}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="bg-white border border-gray-200 rounded-lg p-4 lg:p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Active Briefs
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {statsLoading ? (
                  <span className="inline-block animate-pulse bg-gray-200 h-8 w-12 rounded"></span>
                ) : (
                  stats?.activeBriefs || 0
                )}
              </p>
            </div>
            <div className={`bg-purple-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
              <Briefcase className={`text-purple-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-white border border-gray-200 rounded-lg p-4 lg:p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Pending Proposals
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {statsLoading ? (
                  <span className="inline-block animate-pulse bg-gray-200 h-8 w-12 rounded"></span>
                ) : (
                  stats?.pendingProposals || 0
                )}
              </p>
            </div>
            <div className={`bg-orange-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
              <FileText className={`text-orange-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-white border border-gray-200 rounded-lg p-4 lg:p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Connected Designers
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {statsLoading ? (
                  <span className="inline-block animate-pulse bg-gray-200 h-8 w-12 rounded"></span>
                ) : (
                  stats?.connectedDesigners || 0
                )}
              </p>
            </div>
            <div className={`bg-brown-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
              <Users className={`text-brown-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-white border border-gray-200 rounded-lg p-4 lg:p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Total Projects
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {statsLoading ? (
                  <span className="inline-block animate-pulse bg-gray-200 h-8 w-12 rounded"></span>
                ) : (
                  stats?.totalProjects || 0
                )}
              </p>
            </div>
            <div className={`bg-brown-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
              <Target className={`text-brown-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="bg-white border border-gray-200 rounded-lg p-4 lg:p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                Completed
              </p>
              <p className={`font-bold text-gray-900 ${isMobile ? 'text-lg' : 'text-2xl'}`}>
                {statsLoading ? (
                  <span className="inline-block animate-pulse bg-gray-200 h-8 w-12 rounded"></span>
                ) : (
                  stats?.completedProjects || 0
                )}
              </p>
            </div>
            <div className={`bg-green-50 rounded-full ${isMobile ? 'p-2' : 'p-3'}`}>
              <CheckCircle className={`text-green-600 ${isMobile ? 'h-4 w-4' : 'h-6 w-6'}`} />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Main Dashboard Grid - Optimized Layout */}
      <div className={`grid gap-6 max-w-full overflow-hidden ${
        isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-4'
      }`}>
        {/* Left Column - Main Content */}
        <div className={`space-y-6 min-w-0 ${
          isMobile ? 'order-1' : 'lg:col-span-3'
        }`}>
          {/* Project Briefs Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
            className="bg-white border border-gray-200 rounded-lg"
          >
            <div className={`border-b border-gray-200 ${isMobile ? 'p-4' : 'p-6'}`}>
              <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'justify-between items-center'}`}>
                <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-base' : 'text-lg'}`}>
                  My Project Briefs
                </h3>
                <Link href="/client/briefs">
                  <Button
                    variant="outline"
                    size="sm"
                    className={isMobile ? 'w-full justify-center' : ''}
                  >
                    View All
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
            <div className={isMobile ? 'p-4' : 'p-6'}>
              {briefsLoading ? (
                <div className={`flex items-center justify-center ${isMobile ? 'py-6' : 'py-8'}`}>
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brown-600"></div>
                  <span className={`ml-3 text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}>
                    Loading briefs...
                  </span>
                </div>
              ) : (projectBriefs?.length || 0) === 0 ? (
                <div className={`text-center ${isMobile ? 'py-6' : 'py-8'}`}>
                  <Briefcase className={`text-gray-400 mx-auto mb-4 ${isMobile ? 'h-8 w-8' : 'h-12 w-12'}`} />
                  <h4 className={`font-medium text-gray-900 mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>
                    No project briefs yet
                  </h4>
                  <p className={`text-gray-500 mb-4 ${isMobile ? 'text-sm' : 'text-base'}`}>
                    Submit your first brief to get started with our designers
                  </p>
                  <Link href="/client/briefs/new">
                    <Button
                      className={`bg-brown-600 hover:bg-brown-700 text-white ${
                        isMobile ? 'w-full' : ''
                      }`}
                      size={isMobile ? "sm" : "default"}
                    >
                      <Briefcase className="h-4 w-4 mr-2" />
                      {isMobile ? 'Submit Brief' : 'Submit Your First Brief'}
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className={`space-y-${isMobile ? '3' : '4'}`}>
                  {(projectBriefs || []).map((brief) => (
                    <div
                      key={brief.id}
                      className={`border border-gray-200 rounded-lg hover:bg-gray-50 ${
                        isMobile ? 'p-3' : 'p-4'
                      }`}
                    >
                      <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'items-start justify-between'} mb-3`}>
                        <div className="flex-1">
                          <h4 className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : 'text-base'}`}>
                            {brief.title}
                          </h4>
                          <p className={`text-gray-600 mt-1 line-clamp-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                            {brief.description}
                          </p>
                        </div>
                        <div className={`flex items-center ${isMobile ? 'space-x-1 self-start' : 'space-x-2 ml-4'}`}>
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full border ${getUrgencyColor(brief.urgency)}`}
                          >
                            {isMobile ? brief.urgency.charAt(0).toUpperCase() + brief.urgency.slice(1) : brief.urgency.toUpperCase()}
                          </span>
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(brief.status)}`}
                          >
                            {isMobile
                              ? brief.status.replace("_", " ").charAt(0).toUpperCase() + brief.status.replace("_", " ").slice(1)
                              : brief.status.replace("_", " ").toUpperCase()
                            }
                          </span>
                        </div>
                      </div>

                      <div className={`grid gap-${isMobile ? '2' : '4'} mb-3 text-gray-500 ${
                        isMobile ? 'grid-cols-1 text-xs' : 'grid-cols-2 md:grid-cols-4 text-sm'
                      }`}>
                        <div className="flex items-center">
                          <DollarSign className={`mr-1 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                          <span className="truncate">{brief.budget_range.replace("_", " - $")}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className={`mr-1 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                          <span className="truncate">{brief.timeline_preference.replace("_", " ")}</span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className={`mr-1 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                          <span className="truncate">{formatDate(brief.created_at)}</span>
                        </div>
                        <div className="flex items-center">
                          <FileText className={`mr-1 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                          <span className="truncate">
                            {brief.proposal_count} proposal{brief.proposal_count !== 1 ? "s" : ""}
                          </span>
                        </div>
                      </div>

                      {brief.assigned_designer_name && (
                        <div className={`flex items-center text-gray-600 mb-3 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          <User className={`mr-1 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                          <span className="truncate">Assigned to: {brief.assigned_designer_name}</span>
                        </div>
                      )}

                      <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'items-center justify-between'}`}>
                        <span className={`text-gray-400 ${isMobile ? 'text-xs order-2' : 'text-xs'}`}>
                          Created {formatDate(brief.created_at)}
                        </span>
                        <div className={`flex ${isMobile ? 'space-x-1 order-1' : 'items-center space-x-2'}`}>
                          <Link href={`/client/briefs/${brief.id}`}>
                            <Button
                              variant="outline"
                              size="sm"
                              className={isMobile ? 'flex-1 text-xs px-2' : ''}
                            >
                              <Eye className={`mr-1 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                              {isMobile ? 'View' : 'View Details'}
                            </Button>
                          </Link>
                          {brief.proposal_count > 0 && (
                            <Link href={`/client/briefs/${brief.id}/proposals`}>
                              <Button
                                size="sm"
                                className={`bg-brown-600 hover:bg-brown-700 text-white ${
                                  isMobile ? 'flex-1 text-xs px-2' : ''
                                }`}
                              >
                                <FileText className={`mr-1 ${isMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />
                                {isMobile ? `Proposals (${brief.proposal_count})` : `Review Proposals (${brief.proposal_count})`}
                              </Button>
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>

          {/* Pending Proposals Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.7 }}
            className="bg-white border border-gray-200 rounded-lg"
          >
            <div className={`border-b border-gray-200 ${isMobile ? 'p-4' : 'p-6'}`}>
              <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'justify-between items-center'}`}>
                <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-base' : 'text-lg'}`}>
                  Recent Proposals
                </h3>
                <Link href="/client/proposals">
                  <Button
                    variant="outline"
                    size="sm"
                    className={isMobile ? 'w-full justify-center' : ''}
                  >
                    View All
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
            <div className={isMobile ? 'p-4' : 'p-6'}>
              {proposalsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brown-600"></div>
                  <span className="ml-3 text-gray-600">Loading proposals...</span>
                </div>
              ) : recentProposals.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    No pending proposals
                  </h4>
                  <p className="text-gray-500">
                    Proposals from designers will appear here for your review
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentProposals.map((proposal) => (
                    <div
                      key={proposal.id}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">
                            {proposal.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            For: {proposal.brief_title}
                          </p>
                          <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                            {proposal.description}
                          </p>
                        </div>
                        <div className="ml-4">
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full border flex items-center ${getClientStatusDisplay(proposal.status, proposal.expires_at).color}`}
                          >
                            {getProposalStatusIcon(getClientStatusDisplay(proposal.status, proposal.expires_at).icon)}
                            <span className="ml-1">
                              {getClientStatusDisplay(proposal.status, proposal.expires_at).text}
                            </span>
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4 mb-3">
                        <div className="flex items-center">
                          {proposal.designer_avatar ? (
                            <img
                              src={proposal.designer_avatar}
                              alt={proposal.designer_name}
                              className="h-6 w-6 rounded-full object-cover mr-2"
                            />
                          ) : (
                            <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                              <User className="h-3 w-3 text-gray-500" />
                            </div>
                          )}
                          <span className="text-sm text-gray-600">
                            {proposal.designer_name}
                          </span>
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <DollarSign className="h-4 w-4 mr-1" />$
                          {proposal.total_budget.toLocaleString()}
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <Clock className="h-4 w-4 mr-1" />
                          {proposal.timeline_weeks} weeks
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-400">
                          Submitted {formatDate(proposal.submitted_at)}
                        </span>
                        <div className="flex items-center space-x-2">
                          <Link href={`/client/proposals/${proposal.id}`}>
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 mr-2" />
                              Review
                            </Button>
                          </Link>

                          {/* Enhanced action buttons with expiration handling */}
                          {(() => {
                            const isExpired = proposal.expires_at && new Date(proposal.expires_at) < new Date();
                            const canTakeAction = (proposal.status === 'submitted' || proposal.status === 'under_review') && !isExpired;

                            if (canTakeAction) {
                              return (
                                <>
                                  <Button
                                    onClick={() =>
                                      handleProposalAction(proposal.id, "accept", proposal.brief_id)
                                    }
                                    disabled={acceptProposal.isPending || rejectProposal.isPending}
                                    size="sm"
                                    className="bg-green-600 hover:bg-green-700 text-white disabled:opacity-50"
                                  >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    {acceptProposal.isPending ? 'Accepting...' : 'Accept'}
                                  </Button>
                                  <Button
                                    onClick={() =>
                                      handleProposalAction(proposal.id, "reject")
                                    }
                                    disabled={acceptProposal.isPending || rejectProposal.isPending}
                                    size="sm"
                                    variant="outline"
                                    className="border-red-600 text-red-600 hover:bg-red-50 disabled:opacity-50"
                                  >
                                    <XCircle className="h-4 w-4 mr-2" />
                                    {rejectProposal.isPending ? 'Rejecting...' : 'Reject'}
                                  </Button>
                                </>
                              );
                            }
                            return null;
                          })()}

                          {/* Show status for accepted/rejected proposals */}
                          {proposal.status === 'accepted' && (
                            <div className="flex items-center text-green-600 text-sm font-medium">
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Accepted
                            </div>
                          )}
                          {proposal.status === 'rejected' && (
                            <div className="flex items-center text-red-600 text-sm font-medium">
                              <XCircle className="h-4 w-4 mr-1" />
                              Rejected
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        </div>

        {/* Right Column - Sidebar */}
        <div className={`space-y-${isMobile ? '4' : '6'} ${isMobile ? 'order-2' : ''}`}>
          {/* Bottom Section - Connected Designers & Recent Projects */}
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Connected Designers */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.8 }}
              className="bg-white border border-gray-200 rounded-lg"
            >
            <div className={`border-b border-gray-200 ${isMobile ? 'p-4' : 'p-6'}`}>
              <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'justify-between items-center'}`}>
                <h3 className={`font-semibold text-gray-900 ${isMobile ? 'text-base' : 'text-lg'}`}>
                  Connected Designers
                </h3>
                <Link href="/client/designers">
                  <Button
                    variant="outline"
                    size="sm"
                    className={isMobile ? 'w-full justify-center' : ''}
                  >
                    View All
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
            <div className={isMobile ? 'p-4' : 'p-6'}>
              {designersLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brown-600"></div>
                  <span className="ml-3 text-gray-600">Loading designers...</span>
                </div>
              ) : (connectedDesigners?.length || 0) === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    No connected designers
                  </h4>
                  <p className="text-gray-500 mb-4">
                    Connect with designers to start collaborating
                  </p>
                  <Link href="/client/designers/browse">
                    <Button
                      size="sm"
                      className="bg-brown-600 hover:bg-brown-700 text-white"
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Browse Designers
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className={`space-y-${isMobile ? '3' : '4'}`}>
                  {(connectedDesigners || []).slice(0, 4).map((designer) => (
                    <div
                      key={designer.id}
                      className={`flex items-center border border-gray-200 rounded-lg hover:bg-gray-50 ${
                        isMobile ? 'space-x-2 p-2' : 'space-x-3 p-3'
                      }`}
                    >
                      <div className="flex-shrink-0">
                        {designer.avatar_url ? (
                          <img
                            src={designer.avatar_url}
                            alt={designer.full_name}
                            className={`rounded-full object-cover ${isMobile ? 'h-8 w-8' : 'h-10 w-10'}`}
                          />
                        ) : (
                          <div className={`rounded-full bg-gray-200 flex items-center justify-center ${
                            isMobile ? 'h-8 w-8' : 'h-10 w-10'
                          }`}>
                            <User className={`text-gray-500 ${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className={`font-medium text-gray-900 truncate ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          {designer.full_name}
                        </p>
                        <p className={`text-gray-500 truncate ${isMobile ? 'text-xs' : 'text-xs'}`}>
                          {designer.specialization}
                        </p>
                        <div className="flex items-center mt-1">
                          {renderStars(designer.rating)}
                          <span className={`text-gray-500 ml-2 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                            ({designer.completed_projects} projects)
                          </span>
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <span
                          className={`px-2 py-1 text-xs font-medium rounded-full ${getAvailabilityColor(designer.availability_status)}`}
                        >
                          {isMobile ? designer.availability_status.charAt(0).toUpperCase() : designer.availability_status}
                        </span>
                      </div>
                    </div>
                  ))}

                  <div className="pt-4 border-t border-gray-200">
                    <Link href="/client/briefs/new">
                      <Button className="w-full bg-brown-600 hover:bg-brown-700 text-white">
                        <Send className="h-4 w-4 mr-2" />
                        Send Brief to Designers
                      </Button>
                    </Link>
                  </div>
                </div>
              )}
            </div>
            </motion.div>

            {/* Recent Projects */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.9 }}
              className="bg-white border border-gray-200 rounded-lg"
            >
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Recent Projects
                  </h3>
                  <Link href="/client/projects">
                    <Button variant="outline" size="sm">
                      View All
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
              <div className="p-6">
                {recentProjects.length === 0 ? (
                  <div className="text-center py-8">
                    <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 mb-2">
                      No projects yet
                    </h4>
                    <p className="text-gray-500 mb-4">
                      Start by submitting a project brief
                    </p>
                    <Link href="/client/briefs/new">
                      <Button
                        size="sm"
                        className="bg-brown-600 hover:bg-brown-700 text-white"
                      >
                        <Briefcase className="h-4 w-4 mr-2" />
                        Submit Brief
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentProjects.map((project) => (
                      <Link
                        key={project.id}
                        href={`/client/projects/${project.id}`}
                      >
                        <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {project.title}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatDate(project.updated_at)}
                            </p>
                          </div>
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(project.status)}`}
                          >
                            {project.status.replace("_", " ")}
                          </span>
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>

        {/* Right Column - Compact Sidebar */}
        <div className={`min-w-0 ${
          isMobile ? 'order-2 space-y-4' : 'lg:col-span-1 space-y-4'
        }`}>
          {/* Quick Actions - Compact */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.0 }}
            className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="border-b border-gray-200 p-3 bg-gradient-to-r from-brown-50 to-orange-50">
              <h3 className="font-semibold text-gray-900 text-sm flex items-center gap-2">
                <Briefcase className="h-4 w-4 text-brown-600" />
                Quick Actions
              </h3>
            </div>
            <div className="p-3 grid grid-cols-2 gap-2">
              <Link href="/client/briefs/new">
                <Button
                  variant="outline"
                  className="w-full justify-center border-brown-600 text-brown-600 hover:bg-brown-50"
                  size="sm"
                >
                  <Briefcase className="h-3 w-3" />
                </Button>
              </Link>
              <Link href="/client/messages">
                <Button
                  variant="outline"
                  className="w-full justify-center"
                  size="sm"
                >
                  <MessageSquare className="h-3 w-3" />
                </Button>
              </Link>
              <Link href="/client/projects/new">
                <Button
                  variant="outline"
                  className="w-full justify-center"
                  size="sm"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </Link>
              <Link href="/client/designers/browse">
                <Button
                  variant="outline"
                  className="w-full justify-center"
                  size="sm"
                >
                  <Search className="h-3 w-3" />
                </Button>
              </Link>
            </div>
          </motion.div>

          {/* Communication Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.1 }}
            className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
          >
            <div className="border-b border-gray-200 p-3 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h3 className="font-semibold text-gray-900 text-sm flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-blue-600" />
                Communication
              </h3>
            </div>
            <div className="p-3">
              <UnifiedCommunication role="client" compact={true} />
            </div>
          </motion.div>

          {/* File Manager - Compact */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.2 }}
            className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
          >
            <div className="border-b border-gray-200 p-3 bg-gradient-to-r from-green-50 to-emerald-50">
              <h3 className="font-semibold text-gray-900 text-sm flex items-center gap-2">
                <FileText className="h-4 w-4 text-green-600" />
                Recent Files
              </h3>
            </div>
            <div className="p-3">
              <UnifiedFileManager role="client" compact={true} />
            </div>
          </motion.div>

          {/* Quality & Management - Combined */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 1.3 }}
            className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
          >
            <div className="border-b border-gray-200 p-3 bg-gradient-to-r from-purple-50 to-pink-50">
              <h3 className="font-semibold text-gray-900 text-sm flex items-center gap-2">
                <Star className="h-4 w-4 text-purple-600" />
                Project Status
              </h3>
            </div>
            <div className="p-3 space-y-3">
              <QualityIntegration role="client" userId={user?.id} compact={true} />
              <div className="border-t border-gray-100 pt-3">
                <ManagerIntegration role="client" userId={user?.id} compact={true} />
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Integration Test Component - Only shows in development */}
      <OptimizedAppIntegrationTest />
    </div>
  );
}
